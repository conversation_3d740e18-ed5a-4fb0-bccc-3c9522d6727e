# Complete Shopify-Return Prime Integration Setup Guide

## 🎯 Overview

This guide will help you set up the complete integration between Return Prime and Shopify to automatically identify suppliers and send return notifications.

## 📋 Prerequisites

1. **Shopify Store** with admin access
2. **Return Prime Account** with webhook access
3. **Server/Hosting** to run the integration service
4. **Email Service** (Gmail, SendGrid, etc.) for notifications

## 🔧 Step 1: Shopify API Setup

### 1.1 Create Custom App in Shopify

1. Go to your Shopify Admin: `Settings` → `Apps and sales channels` → `Develop apps`
2. Click "Create an app"
3. Enter app name: "Return Prime Integration"
4. Click "Create app"

### 1.2 Configure API Scopes

Click "Configure Admin API scopes" and enable:
- ✅ `read_products` - To fetch product details and vendor information
- ✅ `read_orders` - To access order information
- ✅ `write_orders` - To create returns/exchanges (optional)

### 1.3 Install and Get Access Token

1. Click "Install app"
2. Copy the "Admin API access token" (starts with `shpat_`)

## 🔧 Step 2: Environment Configuration

Create a `.env` file with these variables:

```env
# Shopify Configuration
SHOPIFY_STORE_URL=your-store-name.myshopify.com
SHOPIFY_ACCESS_TOKEN=shpat_your_access_token_here
SHOPIFY_API_VERSION=2025-07
SHOPIFY_WEBHOOK_SECRET=your_webhook_secret_here

# Return Prime Configuration  
RETURN_PRIME_API_URL=https://api.returnprime.com/v1
RETURN_PRIME_ADMIN_ACCESS_TOKEN=de5fe93b536d04a451edd984d305577e02f3c424a40f8e8c2293a6bc4de229b4
RETURN_PRIME_WEBHOOK_SECRET=your_return_prime_webhook_secret

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Supplier Email Addresses
SMOKEDROP_EMAIL=<EMAIL>
BUDDIFY_EMAIL=<EMAIL>
CANNA_RIVER_EMAIL=<EMAIL>
DISCREET_SMOKER_EMAIL=<EMAIL>
INTERNAL_REVIEW_EMAIL=<EMAIL>
```

## 🔧 Step 3: Test the Integration

### 3.1 Build and Test

```bash
# Install dependencies
npm install

# Build the project
npm run build

# Test the complete integration
node test-complete-integration.js
```

### 3.2 Expected Output

You should see:
- ✅ Shopify API connection successful
- ✅ Products found with vendor information
- ✅ Vendor enrichment working
- ✅ Supplier mapping working

## 🔧 Step 4: Deploy the Service

### 4.1 Start the Server

```bash
# Development
npm run dev

# Production
npm start
```

### 4.2 Available Endpoints

- `GET /` - Service status
- `POST /webhook/return-prime` - Return Prime webhooks
- `POST /webhook/shopify/products/create` - Shopify product webhooks
- `POST /webhook/shopify/products/update` - Shopify product webhooks
- `POST /webhook/shopify/orders/create` - Shopify order webhooks
- `GET /shopify/webhooks/list` - List Shopify webhooks
- `POST /shopify/webhooks/setup` - Setup Shopify webhooks

## 🔧 Step 5: Configure Return Prime Webhooks

### 5.1 Set Up Webhook Endpoint

1. Deploy your service to a public URL (e.g., `https://yourdomain.com`)
2. Configure Return Prime to send webhooks to: `https://yourdomain.com/webhook/return-prime`

### 5.2 Test Return Prime Integration

1. Create a test return in Return Prime
2. Check your server logs for webhook processing
3. Verify vendor information is fetched from Shopify
4. Confirm supplier email notifications are sent

## 🔧 Step 6: Configure Shopify Webhooks (Optional)

### 6.1 Setup Shopify Webhooks

```bash
# Setup webhooks programmatically
curl -X POST http://localhost:3000/shopify/webhooks/setup \
  -H "Content-Type: application/json" \
  -d '{"webhookEndpoint": "https://yourdomain.com/webhook/shopify"}'
```

### 6.2 Manual Webhook Setup

Alternatively, set up webhooks manually in Shopify Admin:
1. Go to `Settings` → `Notifications`
2. Scroll to "Webhooks" section
3. Add webhooks for:
   - Product creation: `https://yourdomain.com/webhook/shopify/products/create`
   - Product updates: `https://yourdomain.com/webhook/shopify/products/update`
   - Order creation: `https://yourdomain.com/webhook/shopify/orders/create`

## 🔄 Complete Workflow

### How It Works

1. **Customer submits return** via Return Prime
2. **Return Prime sends webhook** to your server with basic product info
3. **Your system extracts** `product_id` and `variant_id` from webhook
4. **Shopify API call** fetches vendor information for the product
5. **Supplier mapping** matches vendor to configured supplier
6. **Email notification** sent automatically to correct supplier
7. **Supplier responds** with approval/denial/shipping address

### Example Flow

```
Return Prime Webhook:
{
  "product_id": "9698464760088",
  "sku": "ABC-123",
  "customer_email": "<EMAIL>"
}
        ↓
Shopify API Query:
GET /products/9698464760088
Response: { "vendor": "SmokeDrop" }
        ↓
Supplier Mapping:
"SmokeDrop" → <EMAIL>
        ↓
Email Notification:
"New return request for SmokeDrop product..."
```

## 🔍 Troubleshooting

### Common Issues

1. **Shopify API 404 Errors**
   - Product IDs from Return Prime don't exist in your Shopify store
   - This is normal for test data from different stores

2. **Vendor Information Missing**
   - Ensure products in Shopify have vendor field populated
   - Update product data to include vendor information

3. **Supplier Mapping Issues**
   - Check vendor names match your supplier configuration
   - Add vendor name variations to supplier config

4. **Email Delivery Issues**
   - Verify email credentials and SMTP settings
   - Check spam folders for test emails

### Monitoring

Check logs for:
- ✅ Successful webhook processing
- ✅ Vendor information retrieval
- ✅ Supplier mapping results
- ✅ Email delivery status

## 🚀 Production Deployment

### Security Checklist

- ✅ Enable webhook signature verification
- ✅ Use HTTPS for all endpoints
- ✅ Secure environment variables
- ✅ Set up proper logging and monitoring
- ✅ Configure rate limiting

### Scaling Considerations

- Use queue system for webhook processing
- Implement database for caching product/vendor data
- Set up monitoring and alerting
- Consider load balancing for high volume

## 📞 Support

If you encounter issues:
1. Check the application logs for detailed error messages
2. Run the test script to verify configuration
3. Verify all environment variables are set correctly
4. Test API connections independently
