# Vendor Enrichment Guide

## Overview

This document explains how the enhanced Return Prime integration now automatically fetches vendor information from Shopify to accurately map return requests to the correct suppliers.

## The Problem

Return Prime webhooks provide basic product information but **do not include vendor/supplier details**:

```json
{
  "original_product": {
    "sku": "036265ec-6c5a-4273-9e75-d15e7f50823b",
    "title": "Vessel - Air [Emerald]",
    "product_id": 9698464760088,
    "variant_id": 49924238278936
    // ❌ No vendor information
  }
}
```

## The Solution

### Step 1: Vendor Enrichment from Shopify

When processing a return request, the system now:

1. **Extracts product/variant IDs** from the Return Prime webhook
2. **Queries Shopify GraphQL API** to fetch vendor information:
   ```graphql
   query getProduct($id: ID!) {
     product(id: $id) {
       vendor
       tags
       metafields(first: 10) {
         edges {
           node {
             namespace
             key
             value
           }
         }
       }
     }
   }
   ```
3. **Enriches return items** with actual vendor data from Shopify

### Step 2: Intelligent Supplier Mapping

The enhanced `SupplierMapper` now uses a priority-based approach:

1. **Primary: Vendor Name from Shopify**
   - If Shopify returns `vendor: "SmokeDrop"` → Maps to SmokeDrop supplier
   - If Shopify returns `vendor: "Canna River"` → Maps to Canna River supplier
   - More accurate than SKU-based matching

2. **Fallback: SKU Prefix Matching**
   - If vendor name doesn't match any configured supplier
   - Falls back to existing SKU prefix logic
   - `SMOKEDROP-*`, `CANNA-*`, etc.

3. **Unknown Handling**
   - Items that can't be mapped are sent to internal review team
   - Detailed logging for troubleshooting

## Code Changes

### Enhanced ShopifyService

Added two new methods:

```typescript
// Get product details including vendor
async getProductDetails(productId: string): Promise<{
  success: boolean;
  vendor?: string;
  tags?: string[];
  metafields?: any[];
  error?: string;
}>

// Get variant details including product vendor
async getVariantDetails(variantId: string): Promise<{
  success: boolean;
  productId?: string;
  vendor?: string;
  sku?: string;
  error?: string;
}>
```

### Enhanced ReturnProcessor

Added vendor enrichment logic:

```typescript
// Enrich items with vendor information from Shopify
private async enrichItemsWithVendorInfo(items: ReturnItem[]): Promise<ReturnItem[]>
```

### Enhanced SupplierMapper

Improved mapping logic with vendor name priority:

```typescript
// First try vendor name, then SKU prefix
public mapItemToSupplier(item: ReturnItem): SupplierName
```

## Configuration

### Supplier Configuration

Each supplier now supports multiple vendor names:

```typescript
{
  name: 'SmokeDrop',
  email: '<EMAIL>',
  requiresManualReview: false,
  skuPrefixes: ['SMOKEDROP-', 'SD-'],
  vendorNames: ['SmokeDrop', 'Smoke Drop'] // Multiple variations
}
```

### Environment Variables

Ensure these are set for Shopify integration:

```env
SHOPIFY_STORE_URL=your-store.myshopify.com
SHOPIFY_ACCESS_TOKEN=your_access_token
SHOPIFY_API_VERSION=2023-10
```

## Workflow Example

### Before Enhancement
```
Return Prime Webhook → SKU: "unknown-sku-123" → Unknown Supplier → Manual Review
```

### After Enhancement
```
Return Prime Webhook 
  → product_id: 9698464760088
  → Shopify API Call
  → vendor: "SmokeDrop"
  → SmokeDrop Supplier
  → Automated Email to SmokeDrop
```

## Benefits

1. **Accurate Mapping**: Uses actual vendor data from Shopify instead of guessing from SKUs
2. **Reduced Manual Review**: Fewer items marked as "Unknown"
3. **Better Logging**: Detailed logs for troubleshooting mapping issues
4. **Fallback Support**: Still works if Shopify API fails
5. **Flexible Configuration**: Supports multiple vendor name variations per supplier

## Testing

Use the test script to verify vendor enrichment:

```bash
node test-vendor-enrichment.js
```

This will:
- Test Shopify API connectivity
- Fetch vendor information for sample products
- Demonstrate the mapping process
- Show all configured suppliers

## Troubleshooting

### Common Issues

1. **Shopify API Errors**
   - Check access token permissions
   - Verify store URL format
   - Ensure product/variant IDs are valid

2. **Vendor Name Mismatches**
   - Check `vendorNames` configuration for each supplier
   - Add variations (e.g., "SmokeDrop" and "Smoke Drop")
   - Review logs for unmapped vendor names

3. **Missing Vendor Information**
   - Some products may not have vendor set in Shopify
   - System will fall back to SKU prefix matching
   - Consider updating product data in Shopify

### Logging

The system provides detailed logs:

```
✅ Successfully enriched item with vendor: SmokeDrop
⚠️  Vendor name from Shopify does not match any configured supplier
❌ Could not determine vendor for item
```

## Next Steps

1. **Monitor Logs**: Check for unmapped vendor names
2. **Update Configuration**: Add new suppliers or vendor name variations as needed
3. **Shopify Data Quality**: Ensure products have vendor information set
4. **Performance Monitoring**: Watch for Shopify API rate limits
