{"description": "Sample Return Prime webhook payload - request.refunded event", "timestamp": "2025-07-14T21:33:29.123Z", "headers": {"x-rp-hmac-sha512": "htwJf6gvc3xXWbeK17eYu3ecn0wgrVspYiiW7dPeIU0BnN8i/hMayQdnQGe0XVkb9J048Zf3lhrUWOy0Fs5eKA==", "x-rp-store": "tgm1vh-mn.myshopify.com", "x-rp-topic": "request/refunded"}, "payload": {"request": {"approved": {"comment": null, "created_at": "2025-07-14T16:52:39.932Z", "status": true}, "archived": {"comment": null, "created_at": null, "status": false}, "channel": 97426, "created_at": "2025-07-14T16:48:46.097Z", "customer": {"address": {"address_line_1": "3183 WILSHIRE BLVD", "address_line_2": "213", "address_line_3": "", "city": "Los Angeles", "country": "United States", "country_code": "US", "first_name": "NATHAN KIM", "last_name": null, "postal_code": "90010", "province": "California", "province_code": "CA"}, "bank": {"account_holder_name": "", "account_number": "", "confirm_account_number": "", "ifsc_code": ""}, "email": "<EMAIL>", "id": *************, "name": "<PERSON>", "phone": ""}, "id": "687534ee0e16543e5424053b", "incentive": null, "inspected": {"comment": null, "created_at": "2025-07-14T21:21:29.826Z", "status": true}, "line_items": [{"exchange": {"order": null}, "exchange_fee": {"price_set": {"presentment_money": {"amount": null, "currency_code": null}, "shop_money": {"amount": null, "currency_code": null}}}, "id": **************, "notes": null, "original_product": {"image": {"src": "https://cdn.shopify.com/s/files/1/0919/9881/4488/files/f8a5a49c-0bda-4402-8063-0d2ec43134d3.png?v=**********"}, "price": 20, "product_deleted": false, "product_id": *************, "sku": "036265ec-6c5a-4273-9e75-d15e7f50823b", "title": "Vessel - Air [Emerald]", "variant_deleted": false, "variant_id": **************, "variant_title": null}, "presentment_price": {"actual_amount": 20, "currency": "USD", "do_not_carry_forward_discount": true, "return_quantity": 1, "shipping_amount": 6.95, "taxes_included": false, "total_discount": 0, "total_tax": 0}, "quantity": 1, "reason": "Others", "refund": {"actual_mode": "store_credit", "comment": null, "meta": {"gift_card_code": "rpgem9vfot14072025"}, "refunded_amount": {"presentment_money": {"amount": 20, "currency_code": "USD"}, "shop_money": {"amount": 20, "currency_code": "USD"}}, "refunded_at": "2025-07-14T21:33:27.879Z", "requested_mode": "store_credit", "status": "refunded"}, "return_fee": {"price_set": {"presentment_money": {"amount": 0, "currency_code": "USD"}, "shop_money": {"amount": 0, "currency_code": "USD"}}, "rule": null}, "return_location": null, "shipping": [{"awb": "_", "labels": [], "shipment_status": "Requested", "shipping_company": "other", "tracking_available": false, "tracking_updated_at": null, "tracking_url": ""}], "shop_price": {"actual_amount": 20, "currency": "USD", "do_not_carry_forward_discount": true, "return_quantity": 1, "shipping_amount": 6.95, "taxes_included": false, "total_discount": 0, "total_tax": 0}, "shopify_order_fulfillment_location": null, "tags": []}], "manual_request": true, "order": {"created_at": "2025-05-24T23:12:58.000Z", "fulfillments": [], "id": 6437418565912, "name": "#1003", "order_manual_payment": false, "payment_gateways": []}, "payment_details": null, "received": {"comment": null, "created_at": "2025-07-14T17:00:41.116Z", "status": true}, "rejected": {"comment": null, "created_at": null, "status": false}, "request_number": "RET2", "request_type": "return", "smart_exchange": false, "status": "inspected", "unarchived": {"comment": null, "created_at": null, "status": false}}}, "processing_result": {"event_type_detected": "request.refunded", "idempotency_key": "65ef67b174f292378896d5e0ef4f71041aabef16f34c6a06c54d7373bda08515", "transformed_data": {"return_id": "687534ee0e16543e5424053b", "order_id": "6437418565912", "customer_email": "<EMAIL>", "customer_name": "<PERSON>", "event_type": "request.refunded", "status": "inspected", "refund_amount": 20, "exchange": false, "items": [{"sku": "036265ec-6c5a-4273-9e75-d15e7f50823b", "name": "Vessel - Air [Emerald]", "qty": 1, "reason": "Others", "product_id": "*************", "variant_id": "**************", "line_item_id": "**************"}]}, "notes": ["This webhook shows a refunded return request", "The refund was processed as store credit", "Gift card code: rpgem9vfot14072025", "Refund amount: $20 USD", "The item SKU doesn't match any configured supplier prefixes", "System will create internal notification for manual review"]}}