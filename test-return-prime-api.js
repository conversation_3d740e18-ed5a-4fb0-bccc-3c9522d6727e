const axios = require('axios');

// Return Prime API configuration
const API_KEY = 'de5fe93b536d04a451edd984d305577e02f3c424a40f8e8c2293a6bc4de229b4';
const BASE_URL = 'https://api.returnprime.com/v1';

// Create axios instance with authentication
const client = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

async function testReturnPrimeAPI() {
  console.log('🔍 Testing Return Prime API endpoints...\n');

  try {
    // Test 1: Get return details from webhook sample
    const returnId = '687534ee0e16543e5424053b'; // From webhook sample data
    console.log(`📋 Testing return details for ID: ${returnId}`);
    
    try {
      const returnResponse = await client.get(`/returns/${returnId}`);
      console.log('✅ Return details response:');
      console.log(JSON.stringify(returnResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Return details error:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Get all returns to see data structure
    console.log('📋 Testing get all returns...');
    try {
      const allReturnsResponse = await client.get('/returns', {
        params: { limit: 5 } // Get just a few to see structure
      });
      console.log('✅ All returns response:');
      console.log(JSON.stringify(allReturnsResponse.data, null, 2));
    } catch (error) {
      console.log('❌ All returns error:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: Get order details to see if vendor info is there
    const orderId = '6437418565912'; // From webhook sample data
    console.log(`📋 Testing order details for ID: ${orderId}`);
    
    try {
      const orderResponse = await client.get(`/orders/${orderId}`);
      console.log('✅ Order details response:');
      console.log(JSON.stringify(orderResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Order details error:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 4: Get products to see if vendor info is available
    console.log('📋 Testing products endpoint...');
    try {
      const productsResponse = await client.get('/products', {
        params: { limit: 5 }
      });
      console.log('✅ Products response:');
      console.log(JSON.stringify(productsResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Products error:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 5: Get specific product details
    const productId = '9698464760088'; // From webhook sample data
    console.log(`📋 Testing product details for ID: ${productId}`);
    
    try {
      const productResponse = await client.get(`/products/${productId}`);
      console.log('✅ Product details response:');
      console.log(JSON.stringify(productResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Product details error:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 6: Check if there's a vendors/suppliers endpoint
    console.log('📋 Testing vendors/suppliers endpoint...');
    try {
      const vendorsResponse = await client.get('/vendors');
      console.log('✅ Vendors response:');
      console.log(JSON.stringify(vendorsResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Vendors error:', error.response?.data || error.message);
    }

    try {
      const suppliersResponse = await client.get('/suppliers');
      console.log('✅ Suppliers response:');
      console.log(JSON.stringify(suppliersResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Suppliers error:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 7: Check API documentation or schema endpoint
    console.log('📋 Testing API schema/docs endpoints...');
    try {
      const schemaResponse = await client.get('/schema');
      console.log('✅ Schema response:');
      console.log(JSON.stringify(schemaResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Schema error:', error.response?.data || error.message);
    }

    try {
      const docsResponse = await client.get('/docs');
      console.log('✅ Docs response:');
      console.log(JSON.stringify(docsResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Docs error:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ General API test error:', error.message);
  }
}

// Run the test
testReturnPrimeAPI().then(() => {
  console.log('\n🏁 API testing completed!');
}).catch(error => {
  console.error('💥 Test script failed:', error.message);
});
