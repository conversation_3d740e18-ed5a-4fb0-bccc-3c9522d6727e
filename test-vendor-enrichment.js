const { ShopifyService } = require('./dist/services/ShopifyService');
const { SupplierMapper } = require('./dist/services/SupplierMapper');
const { config } = require('./dist/config');

// Test data from webhook sample
const testReturnItems = [
  {
    sku: "036265ec-6c5a-4273-9e75-d15e7f50823b",
    name: "Vessel - Air [Emerald]",
    qty: 1,
    reason: "Others",
    product_id: "9698464760088",
    variant_id: "49924238278936",
    line_item_id: "15872439091480"
  }
];

async function testVendorEnrichment() {
  console.log('🧪 Testing Vendor Enrichment from Shopify...\n');

  try {
    // Initialize services
    const shopifyService = new ShopifyService();
    const supplierMapper = new SupplierMapper();

    console.log('📋 Test Return Item:');
    console.log(JSON.stringify(testReturnItems[0], null, 2));
    console.log('\n' + '='.repeat(50) + '\n');

    // Test 1: Get product details
    console.log('🔍 Step 1: Fetching product details from Shopify...');
    const productDetails = await shopifyService.getProductDetails(testReturnItems[0].product_id);
    
    if (productDetails.success) {
      console.log('✅ Product details retrieved:');
      console.log(`   Vendor: ${productDetails.vendor || 'Not specified'}`);
      console.log(`   Tags: ${productDetails.tags?.join(', ') || 'None'}`);
      console.log(`   Metafields: ${productDetails.metafields?.length || 0} found`);
    } else {
      console.log('❌ Failed to get product details:', productDetails.error);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Get variant details
    console.log('🔍 Step 2: Fetching variant details from Shopify...');
    const variantDetails = await shopifyService.getVariantDetails(testReturnItems[0].variant_id);
    
    if (variantDetails.success) {
      console.log('✅ Variant details retrieved:');
      console.log(`   Product ID: ${variantDetails.productId || 'Not found'}`);
      console.log(`   Vendor: ${variantDetails.vendor || 'Not specified'}`);
      console.log(`   SKU: ${variantDetails.sku || 'Not found'}`);
    } else {
      console.log('❌ Failed to get variant details:', variantDetails.error);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: Enrich item with vendor info
    console.log('🔍 Step 3: Enriching item with vendor information...');
    let enrichedItem = { ...testReturnItems[0] };
    
    // Add vendor from product details if available
    if (productDetails.success && productDetails.vendor) {
      enrichedItem.vendor_name = productDetails.vendor;
      console.log(`✅ Item enriched with vendor: ${productDetails.vendor}`);
    } else if (variantDetails.success && variantDetails.vendor) {
      enrichedItem.vendor_name = variantDetails.vendor;
      console.log(`✅ Item enriched with vendor from variant: ${variantDetails.vendor}`);
    } else {
      console.log('❌ No vendor information found');
    }

    console.log('\n📋 Enriched Item:');
    console.log(JSON.stringify(enrichedItem, null, 2));

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 4: Map to supplier
    console.log('🔍 Step 4: Mapping item to supplier...');
    const supplierName = supplierMapper.mapItemToSupplier(enrichedItem);
    console.log(`✅ Mapped to supplier: ${supplierName}`);

    // Test 5: Get supplier configuration
    if (supplierName !== 'Unknown') {
      const supplierConfig = supplierMapper.getSupplierConfig(supplierName);
      if (supplierConfig) {
        console.log('\n📋 Supplier Configuration:');
        console.log(`   Name: ${supplierConfig.name}`);
        console.log(`   Email: ${supplierConfig.email}`);
        console.log(`   Requires Manual Review: ${supplierConfig.requiresManualReview}`);
        console.log(`   SKU Prefixes: ${supplierConfig.skuPrefixes.join(', ')}`);
        console.log(`   Vendor Names: ${supplierConfig.vendorNames.join(', ')}`);
      }
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 6: Show all configured suppliers for reference
    console.log('📋 All Configured Suppliers:');
    config.suppliers.forEach((supplier, index) => {
      console.log(`   ${index + 1}. ${supplier.name}`);
      console.log(`      Email: ${supplier.email}`);
      console.log(`      Vendor Names: ${supplier.vendorNames.join(', ')}`);
      console.log(`      SKU Prefixes: ${supplier.skuPrefixes.join(', ')}`);
      console.log(`      Manual Review: ${supplier.requiresManualReview}`);
      console.log('');
    });

  } catch (error) {
    console.error('💥 Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testVendorEnrichment().then(() => {
  console.log('🏁 Vendor enrichment test completed!');
}).catch(error => {
  console.error('💥 Test script failed:', error.message);
});
