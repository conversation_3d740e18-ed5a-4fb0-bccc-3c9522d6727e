const axios = require('axios');

// Test data from webhook sample
const webhookData = {
  "product_id": "9698464760088",
  "variant_id": "49924238278936", 
  "sku": "036265ec-6c5a-4273-9e75-d15e7f50823b",
  "title": "Vessel - Air [Emerald]",
  "price": 20,
  "customer_email": "<EMAIL>",
  "customer_name": "<PERSON>",
  "order_id": "6437418565912",
  "return_id": "687534ee0e16543e5424053b",
  "return_reason": "Others"
};

async function testProductLookupEndpoint() {
  console.log('🧪 Testing Product Lookup Endpoint...\n');

  const baseURL = 'http://localhost:3000';

  try {
    // Test 1: Test endpoint with sample data
    console.log('📋 Test 1: GET /api/product-lookup/test');
    console.log('   Using sample webhook data...');
    
    try {
      const testResponse = await axios.get(`${baseURL}/api/product-lookup/test`);
      console.log('✅ Test endpoint successful!');
      console.log('📊 Response:');
      console.log(JSON.stringify(testResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Test endpoint failed:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(60) + '\n');

    // Test 2: POST endpoint with webhook data
    console.log('📋 Test 2: POST /api/product-lookup');
    console.log('   Sending webhook data...');
    console.log('📦 Input data:');
    console.log(JSON.stringify(webhookData, null, 2));
    
    try {
      const postResponse = await axios.post(`${baseURL}/api/product-lookup`, webhookData);
      console.log('✅ POST endpoint successful!');
      console.log('📊 Response:');
      console.log(JSON.stringify(postResponse.data, null, 2));

      // Analyze the response
      const data = postResponse.data;
      console.log('\n📋 Analysis:');
      console.log(`   Success: ${data.success ? '✅' : '❌'}`);
      console.log(`   Shopify Vendor: ${data.shopify_data?.vendor || 'Not found'}`);
      console.log(`   Mapped Supplier: ${data.supplier_mapping?.supplier_name || 'Unknown'}`);
      console.log(`   Supplier Email: ${data.supplier_mapping?.supplier_email || 'Not available'}`);
      console.log(`   Mapping Method: ${data.supplier_mapping?.mapping_method || 'Unknown'}`);
      console.log(`   Manual Review Required: ${data.supplier_mapping?.requires_manual_review ? 'Yes' : 'No'}`);

    } catch (error) {
      console.log('❌ POST endpoint failed:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(60) + '\n');

    // Test 3: Get all suppliers
    console.log('📋 Test 3: GET /api/suppliers');
    console.log('   Getting all configured suppliers...');
    
    try {
      const suppliersResponse = await axios.get(`${baseURL}/api/suppliers`);
      console.log('✅ Suppliers endpoint successful!');
      console.log(`📊 Found ${suppliersResponse.data.count} suppliers:`);
      
      suppliersResponse.data.suppliers.forEach((supplier, index) => {
        console.log(`   ${index + 1}. ${supplier.name}`);
        console.log(`      📧 Email: ${supplier.email}`);
        console.log(`      🏷️  Vendor Names: ${supplier.vendor_names.join(', ')}`);
        console.log(`      🔖 SKU Prefixes: ${supplier.sku_prefixes.join(', ')}`);
        console.log(`      ⚙️  Manual Review: ${supplier.requires_manual_review ? 'Yes' : 'No'}`);
        console.log('');
      });
    } catch (error) {
      console.log('❌ Suppliers endpoint failed:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(60) + '\n');

    // Test 4: Test with different product data
    console.log('📋 Test 4: Testing with different product scenarios...');
    
    const testScenarios = [
      {
        name: 'Product ID only',
        data: { product_id: webhookData.product_id }
      },
      {
        name: 'Variant ID only', 
        data: { variant_id: webhookData.variant_id }
      },
      {
        name: 'SKU only',
        data: { sku: webhookData.sku }
      },
      {
        name: 'No identifiers (should fail)',
        data: { title: 'Test Product' }
      }
    ];

    for (const scenario of testScenarios) {
      console.log(`\n🔍 Testing: ${scenario.name}`);
      try {
        const response = await axios.post(`${baseURL}/api/product-lookup`, scenario.data);
        console.log(`   ✅ Success: ${response.data.success}`);
        console.log(`   📦 Vendor: ${response.data.shopify_data?.vendor || 'Not found'}`);
        console.log(`   🎯 Supplier: ${response.data.supplier_mapping?.supplier_name || 'Unknown'}`);
      } catch (error) {
        console.log(`   ❌ Failed: ${error.response?.status} - ${error.response?.data?.error || error.message}`);
      }
    }

  } catch (error) {
    console.error('💥 Test script failed:', error.message);
  }
}

async function checkServerStatus() {
  console.log('🔍 Checking server status...\n');
  
  try {
    const response = await axios.get('http://localhost:3000/');
    console.log('✅ Server is running!');
    console.log(`   Service: ${response.data.service}`);
    console.log(`   Status: ${response.data.status}`);
    console.log(`   Version: ${response.data.version}`);
    console.log('');
    return true;
  } catch (error) {
    console.log('❌ Server is not running!');
    console.log('   Please start the server with: npm start');
    console.log('   Or in development mode: npm run dev');
    return false;
  }
}

// Main execution
async function main() {
  console.log('🚀 Product Lookup Endpoint Test\n');
  
  const serverRunning = await checkServerStatus();
  
  if (serverRunning) {
    await testProductLookupEndpoint();
  }
  
  console.log('\n🏁 Test completed!');
  console.log('\n📋 Available Endpoints:');
  console.log('   GET  /api/product-lookup/test    - Test with sample data');
  console.log('   POST /api/product-lookup         - Lookup product vendor details');
  console.log('   GET  /api/suppliers              - Get all configured suppliers');
  console.log('   GET  /                           - Health check');
  console.log('   POST /webhook/return-prime       - Return Prime webhook handler');
}

main().catch(error => {
  console.error('💥 Script failed:', error.message);
});
