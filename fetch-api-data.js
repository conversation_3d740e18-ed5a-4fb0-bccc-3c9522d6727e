const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// API Configuration
const RETURN_PRIME_API_KEY = 'de5fe93b536d04a451edd984d305577e02f3c424a40f8e8c2293a6bc4de229b4';
const RETURN_PRIME_BASE_URL = 'https://api.returnprime.com/v1';

const SHOPIFY_STORE_URL = process.env.SHOPIFY_STORE_URL;
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ACCESS_TOKEN;
const SHOPIFY_API_VERSION = process.env.SHOPIFY_API_VERSION || '2025-07';

// Create output directory
const outputDir = './api-data-samples';
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir);
}

// Helper function to save JSON data
function saveJsonFile(filename, data) {
  const filePath = path.join(outputDir, filename);
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  console.log(`✅ Saved: ${filePath}`);
}

// Helper function to handle API errors
function handleApiError(error, apiName) {
  console.log(`❌ ${apiName} API Error:`, error.message);
  if (error.response) {
    console.log(`   Status: ${error.response.status}`);
    console.log(`   Data:`, error.response.data);
  }
  return {
    error: true,
    message: error.message,
    status: error.response?.status,
    data: error.response?.data
  };
}

async function fetchReturnPrimeData() {
  console.log('🔍 Fetching Return Prime API Data...\n');

  const client = axios.create({
    baseURL: RETURN_PRIME_BASE_URL,
    headers: {
      'Authorization': `Bearer ${RETURN_PRIME_API_KEY}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  });

  const results = {};

  // Test 1: Get all returns
  console.log('📋 Fetching returns list...');
  try {
    const response = await client.get('/returns', {
      params: { limit: 10 }
    });
    results.returns_list = response.data;
    console.log(`✅ Found ${response.data?.data?.length || 0} returns`);
    saveJsonFile('return-prime-returns-list.json', response.data);
  } catch (error) {
    results.returns_list = handleApiError(error, 'Return Prime Returns List');
  }

  // Test 2: Get specific return (if we have any)
  if (results.returns_list?.data?.length > 0) {
    const firstReturn = results.returns_list.data[0];
    console.log(`📋 Fetching details for return: ${firstReturn.id}`);
    try {
      const response = await client.get(`/returns/${firstReturn.id}`);
      results.return_details = response.data;
      console.log(`✅ Got return details for: ${firstReturn.id}`);
      saveJsonFile('return-prime-return-details.json', response.data);
    } catch (error) {
      results.return_details = handleApiError(error, 'Return Prime Return Details');
    }
  }

  // Test 3: Get orders
  console.log('📋 Fetching orders list...');
  try {
    const response = await client.get('/orders', {
      params: { limit: 10 }
    });
    results.orders_list = response.data;
    console.log(`✅ Found ${response.data?.data?.length || 0} orders`);
    saveJsonFile('return-prime-orders-list.json', response.data);
  } catch (error) {
    results.orders_list = handleApiError(error, 'Return Prime Orders List');
  }

  // Test 4: Get products
  console.log('📋 Fetching products list...');
  try {
    const response = await client.get('/products', {
      params: { limit: 10 }
    });
    results.products_list = response.data;
    console.log(`✅ Found ${response.data?.data?.length || 0} products`);
    saveJsonFile('return-prime-products-list.json', response.data);
  } catch (error) {
    results.products_list = handleApiError(error, 'Return Prime Products List');
  }

  // Test 5: Get customers
  console.log('📋 Fetching customers list...');
  try {
    const response = await client.get('/customers', {
      params: { limit: 10 }
    });
    results.customers_list = response.data;
    console.log(`✅ Found ${response.data?.data?.length || 0} customers`);
    saveJsonFile('return-prime-customers-list.json', response.data);
  } catch (error) {
    results.customers_list = handleApiError(error, 'Return Prime Customers List');
  }

  // Test 6: Try to get API schema/endpoints
  console.log('📋 Fetching API schema...');
  try {
    const response = await client.get('/');
    results.api_root = response.data;
    console.log(`✅ Got API root response`);
    saveJsonFile('return-prime-api-root.json', response.data);
  } catch (error) {
    results.api_root = handleApiError(error, 'Return Prime API Root');
  }

  // Save complete Return Prime results
  saveJsonFile('return-prime-complete-results.json', results);
  return results;
}

async function fetchShopifyData() {
  console.log('\n🔍 Fetching Shopify API Data...\n');

  if (!SHOPIFY_STORE_URL || !SHOPIFY_ACCESS_TOKEN) {
    console.log('❌ Missing Shopify credentials');
    return { error: 'Missing Shopify credentials' };
  }

  const baseURL = `https://${SHOPIFY_STORE_URL}/admin/api/${SHOPIFY_API_VERSION}`;
  const headers = {
    'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
    'Content-Type': 'application/json'
  };

  const results = {};

  // Test 1: Shop info
  console.log('🏪 Fetching shop information...');
  try {
    const response = await axios.get(`${baseURL}/shop.json`, { headers });
    results.shop_info = response.data;
    console.log(`✅ Got shop info: ${response.data.shop?.name}`);
    saveJsonFile('shopify-shop-info.json', response.data);
  } catch (error) {
    results.shop_info = handleApiError(error, 'Shopify Shop Info');
  }

  // Test 2: Products
  console.log('📦 Fetching products...');
  try {
    const response = await axios.get(`${baseURL}/products.json?limit=10`, { headers });
    results.products = response.data;
    console.log(`✅ Found ${response.data.products?.length || 0} products`);
    saveJsonFile('shopify-products.json', response.data);

    // Get detailed info for first product
    if (response.data.products?.length > 0) {
      const firstProduct = response.data.products[0];
      console.log(`📦 Fetching detailed info for product: ${firstProduct.id}`);
      try {
        const detailResponse = await axios.get(`${baseURL}/products/${firstProduct.id}.json`, { headers });
        results.product_details = detailResponse.data;
        console.log(`✅ Got product details: ${detailResponse.data.product?.title}`);
        saveJsonFile('shopify-product-details.json', detailResponse.data);
      } catch (error) {
        results.product_details = handleApiError(error, 'Shopify Product Details');
      }
    }
  } catch (error) {
    results.products = handleApiError(error, 'Shopify Products');
  }

  // Test 3: Orders
  console.log('📋 Fetching orders...');
  try {
    const response = await axios.get(`${baseURL}/orders.json?limit=10&status=any`, { headers });
    results.orders = response.data;
    console.log(`✅ Found ${response.data.orders?.length || 0} orders`);
    saveJsonFile('shopify-orders.json', response.data);

    // Get detailed info for first order
    if (response.data.orders?.length > 0) {
      const firstOrder = response.data.orders[0];
      console.log(`📋 Fetching detailed info for order: ${firstOrder.id}`);
      try {
        const detailResponse = await axios.get(`${baseURL}/orders/${firstOrder.id}.json`, { headers });
        results.order_details = detailResponse.data;
        console.log(`✅ Got order details: ${detailResponse.data.order?.order_number}`);
        saveJsonFile('shopify-order-details.json', detailResponse.data);
      } catch (error) {
        results.order_details = handleApiError(error, 'Shopify Order Details');
      }
    }
  } catch (error) {
    results.orders = handleApiError(error, 'Shopify Orders');
  }

  // Test 4: Variants
  console.log('🔖 Fetching product variants...');
  try {
    const response = await axios.get(`${baseURL}/variants.json?limit=10`, { headers });
    results.variants = response.data;
    console.log(`✅ Found ${response.data.variants?.length || 0} variants`);
    saveJsonFile('shopify-variants.json', response.data);
  } catch (error) {
    results.variants = handleApiError(error, 'Shopify Variants');
  }

  // Test 5: Customers
  console.log('👥 Fetching customers...');
  try {
    const response = await axios.get(`${baseURL}/customers.json?limit=10`, { headers });
    results.customers = response.data;
    console.log(`✅ Found ${response.data.customers?.length || 0} customers`);
    saveJsonFile('shopify-customers.json', response.data);
  } catch (error) {
    results.customers = handleApiError(error, 'Shopify Customers');
  }

  // Save complete Shopify results
  saveJsonFile('shopify-complete-results.json', results);
  return results;
}

async function generateDataMappingReport(returnPrimeData, shopifyData) {
  console.log('\n📊 Generating Data Mapping Report...\n');

  const report = {
    timestamp: new Date().toISOString(),
    return_prime_summary: {
      api_accessible: !returnPrimeData.error,
      returns_found: returnPrimeData.returns_list?.data?.length || 0,
      orders_found: returnPrimeData.orders_list?.data?.length || 0,
      products_found: returnPrimeData.products_list?.data?.length || 0,
      customers_found: returnPrimeData.customers_list?.data?.length || 0
    },
    shopify_summary: {
      api_accessible: !shopifyData.error,
      shop_name: shopifyData.shop_info?.shop?.name || 'Unknown',
      products_found: shopifyData.products?.products?.length || 0,
      orders_found: shopifyData.orders?.orders?.length || 0,
      customers_found: shopifyData.customers?.customers?.length || 0
    },
    vendor_mapping_analysis: {
      shopify_products_with_vendor: 0,
      unique_vendors: [],
      sample_vendor_data: []
    },
    integration_recommendations: []
  };

  // Analyze Shopify vendor data
  if (shopifyData.products?.products) {
    const productsWithVendor = shopifyData.products.products.filter(p => p.vendor);
    report.vendor_mapping_analysis.shopify_products_with_vendor = productsWithVendor.length;
    
    const vendors = [...new Set(shopifyData.products.products.map(p => p.vendor).filter(Boolean))];
    report.vendor_mapping_analysis.unique_vendors = vendors;
    
    report.vendor_mapping_analysis.sample_vendor_data = shopifyData.products.products.slice(0, 5).map(p => ({
      product_id: p.id,
      title: p.title,
      vendor: p.vendor || 'Not specified',
      handle: p.handle,
      variants_count: p.variants?.length || 0
    }));
  }

  // Generate recommendations
  if (report.return_prime_summary.api_accessible && report.shopify_summary.api_accessible) {
    report.integration_recommendations.push('✅ Both APIs are accessible - integration is possible');
    
    if (report.vendor_mapping_analysis.shopify_products_with_vendor > 0) {
      report.integration_recommendations.push('✅ Shopify products have vendor information - automatic mapping possible');
    } else {
      report.integration_recommendations.push('⚠️ No vendor information found in Shopify products - manual mapping required');
    }
    
    if (report.vendor_mapping_analysis.unique_vendors.length > 0) {
      report.integration_recommendations.push(`📋 Found ${report.vendor_mapping_analysis.unique_vendors.length} unique vendors: ${report.vendor_mapping_analysis.unique_vendors.join(', ')}`);
    }
  } else {
    if (!report.return_prime_summary.api_accessible) {
      report.integration_recommendations.push('❌ Return Prime API not accessible - check API key');
    }
    if (!report.shopify_summary.api_accessible) {
      report.integration_recommendations.push('❌ Shopify API not accessible - check store URL and access token');
    }
  }

  saveJsonFile('data-mapping-report.json', report);
  
  console.log('📊 Data Mapping Report:');
  console.log(`   Return Prime API: ${report.return_prime_summary.api_accessible ? '✅ Accessible' : '❌ Not accessible'}`);
  console.log(`   Shopify API: ${report.shopify_summary.api_accessible ? '✅ Accessible' : '❌ Not accessible'}`);
  console.log(`   Shopify Products with Vendor: ${report.vendor_mapping_analysis.shopify_products_with_vendor}`);
  console.log(`   Unique Vendors: ${report.vendor_mapping_analysis.unique_vendors.length}`);
  console.log('\n📋 Recommendations:');
  report.integration_recommendations.forEach(rec => console.log(`   ${rec}`));

  return report;
}

async function main() {
  console.log('🚀 Fetching API Data from Return Prime and Shopify...\n');
  console.log(`📁 Output directory: ${outputDir}\n`);

  try {
    // Fetch data from both APIs
    const returnPrimeData = await fetchReturnPrimeData();
    const shopifyData = await fetchShopifyData();

    // Generate mapping report
    const report = await generateDataMappingReport(returnPrimeData, shopifyData);

    console.log('\n🎉 Data fetching completed!');
    console.log(`📁 Check the '${outputDir}' directory for all JSON files`);
    console.log('\n📋 Generated Files:');
    
    const files = fs.readdirSync(outputDir);
    files.forEach(file => {
      console.log(`   📄 ${file}`);
    });

  } catch (error) {
    console.error('💥 Script failed:', error.message);
  }
}

// Run the script
main();
