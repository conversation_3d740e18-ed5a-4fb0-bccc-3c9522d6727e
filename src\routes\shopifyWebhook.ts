import { Router, Request, Response } from 'express';
import { ShopifyWebhookService } from '../services/ShopifyWebhookService';
import { captureRawBody } from '../middleware/webhook';
import logger from '../utils/logger';

const router = Router();
const shopifyWebhookService = new ShopifyWebhookService();

/**
 * Middleware to verify Shopify webhook signature
 */
const verifyShopifyWebhook = (req: Request, res: Response, next: any): void => {
  const signature = req.headers['x-shopify-hmac-sha256'] as string;
  const rawBody = (req as any).rawBody;

  if (!signature) {
    logger.warn('Missing Shopify webhook signature');
    res.status(401).json({ error: 'Missing signature' });
    return;
  }

  if (!rawBody) {
    logger.warn('Missing raw body for Shopify webhook verification');
    res.status(400).json({ error: 'Missing raw body' });
    return;
  }

  const isValid = shopifyWebhookService.verifyWebhook(rawBody, signature);
  if (!isValid) {
    logger.warn('Invalid Shopify webhook signature');
    res.status(401).json({ error: 'Invalid signature' });
    return;
  }

  next();
};

/**
 * Handle product creation webhook
 */
router.post('/webhook/shopify/products/create',
  captureRawBody,
  verifyShopifyWebhook,
  async (req: Request, res: Response) => {
    try {
      const product = req.body;
      
      logger.info('Received Shopify product creation webhook', {
        productId: product.id,
        title: product.title,
        vendor: product.vendor,
        handle: product.handle
      });

      // Here you could update your local product cache or database
      // For now, we'll just log the vendor information
      if (product.vendor) {
        logger.info('Product vendor information available', {
          productId: product.id,
          vendor: product.vendor,
          title: product.title
        });
      }

      res.json({ success: true, message: 'Product creation webhook processed' });
    } catch (error) {
      logger.error('Error processing Shopify product creation webhook', { error });
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

/**
 * Handle product update webhook
 */
router.post('/webhook/shopify/products/update',
  captureRawBody,
  verifyShopifyWebhook,
  async (req: Request, res: Response) => {
    try {
      const product = req.body;
      
      logger.info('Received Shopify product update webhook', {
        productId: product.id,
        title: product.title,
        vendor: product.vendor,
        handle: product.handle
      });

      // Update local cache if vendor information changed
      if (product.vendor) {
        logger.info('Product vendor information updated', {
          productId: product.id,
          vendor: product.vendor,
          title: product.title
        });
      }

      res.json({ success: true, message: 'Product update webhook processed' });
    } catch (error) {
      logger.error('Error processing Shopify product update webhook', { error });
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

/**
 * Handle order creation webhook
 */
router.post('/webhook/shopify/orders/create',
  captureRawBody,
  verifyShopifyWebhook,
  async (req: Request, res: Response) => {
    try {
      const order = req.body;
      
      logger.info('Received Shopify order creation webhook', {
        orderId: order.id,
        orderNumber: order.order_number,
        customerEmail: order.email,
        lineItemCount: order.line_items?.length || 0
      });

      // Log vendor information for each line item
      if (order.line_items) {
        order.line_items.forEach((item: any) => {
          logger.info('Order line item vendor info', {
            orderId: order.id,
            productId: item.product_id,
            variantId: item.variant_id,
            sku: item.sku,
            vendor: item.vendor || 'Not specified',
            title: item.title
          });
        });
      }

      res.json({ success: true, message: 'Order creation webhook processed' });
    } catch (error) {
      logger.error('Error processing Shopify order creation webhook', { error });
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

/**
 * Handle order update webhook
 */
router.post('/webhook/shopify/orders/updated',
  captureRawBody,
  verifyShopifyWebhook,
  async (req: Request, res: Response) => {
    try {
      const order = req.body;
      
      logger.info('Received Shopify order update webhook', {
        orderId: order.id,
        orderNumber: order.order_number,
        customerEmail: order.email,
        financialStatus: order.financial_status,
        fulfillmentStatus: order.fulfillment_status
      });

      res.json({ success: true, message: 'Order update webhook processed' });
    } catch (error) {
      logger.error('Error processing Shopify order update webhook', { error });
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

/**
 * Handle order paid webhook
 */
router.post('/webhook/shopify/orders/paid',
  captureRawBody,
  verifyShopifyWebhook,
  async (req: Request, res: Response) => {
    try {
      const order = req.body;
      
      logger.info('Received Shopify order paid webhook', {
        orderId: order.id,
        orderNumber: order.order_number,
        customerEmail: order.email,
        totalPrice: order.total_price
      });

      res.json({ success: true, message: 'Order paid webhook processed' });
    } catch (error) {
      logger.error('Error processing Shopify order paid webhook', { error });
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

export default router;
