import { createAdminApiClient } from '@shopify/admin-api-client';
import { ReturnItem, ShopifyReturnRequest } from '../types';
import { config } from '../config';
import logger from '../utils/logger';

export class ShopifyService {
  private client: any;

  constructor() {
    this.client = createAdminApiClient({
      storeDomain: config.shopify.storeUrl,
      accessToken: config.shopify.accessToken,
      apiVersion: config.shopify.apiVersion
    });
  }

  /**
   * Create a return in Shopify for approved exchanges
   */
  public async createReturn(returnRequest: ShopifyReturnRequest): Promise<{ success: boolean; returnId?: string; error?: string }> {
    try {
      const mutation = `
        mutation returnCreate($input: ReturnInput!) {
          returnCreate(input: $input) {
            return {
              id
              name
              status
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        input: {
          orderId: `gid://shopify/Order/${returnRequest.orderId}`,
          returnLineItems: returnRequest.lineItems.map(item => ({
            fulfillmentLineItemId: `gid://shopify/FulfillmentLineItem/${item.lineItemId}`,
            quantity: item.quantity,
            returnReason: item.reason,
            returnReasonNote: item.reason
          })),
          notifyCustomer: returnRequest.notifyCustomer,
          note: returnRequest.note
        }
      };

      const response = await this.client.request(mutation, { variables });

      if (response.data?.returnCreate?.userErrors?.length > 0) {
        const errors = response.data.returnCreate.userErrors;
        logger.error('Shopify return creation failed with user errors', {
          orderId: returnRequest.orderId,
          errors
        });
        return {
          success: false,
          error: errors.map((e: any) => e.message).join(', ')
        };
      }

      const returnData = response.data?.returnCreate?.return;
      if (!returnData) {
        logger.error('Shopify return creation failed - no return data', {
          orderId: returnRequest.orderId,
          response: response.data
        });
        return {
          success: false,
          error: 'Failed to create return - no return data received'
        };
      }

      logger.info('Shopify return created successfully', {
        orderId: returnRequest.orderId,
        returnId: returnData.id,
        returnName: returnData.name,
        status: returnData.status
      });

      return {
        success: true,
        returnId: returnData.id
      };

    } catch (error) {
      logger.error('Error creating Shopify return', {
        error,
        orderId: returnRequest.orderId
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get order details from Shopify
   */
  public async getOrderDetails(orderId: string): Promise<{ success: boolean; order?: any; error?: string }> {
    try {
      const query = `
        query getOrder($id: ID!) {
          order(id: $id) {
            id
            name
            email
            createdAt
            totalPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            customer {
              id
              firstName
              lastName
              email
            }
            lineItems(first: 250) {
              edges {
                node {
                  id
                  name
                  quantity
                  sku
                  variant {
                    id
                    sku
                  }
                  product {
                    id
                    title
                    vendor
                  }
                }
              }
            }
            fulfillments {
              id
              status
              trackingInfo {
                number
                company
              }
              fulfillmentLineItems(first: 250) {
                edges {
                  node {
                    id
                    lineItem {
                      id
                    }
                    quantity
                  }
                }
              }
            }
          }
        }
      `;

      const variables = {
        id: `gid://shopify/Order/${orderId}`
      };

      const response = await this.client.request(query, { variables });

      if (!response.data?.order) {
        logger.warn('Order not found in Shopify', { orderId });
        return {
          success: false,
          error: 'Order not found'
        };
      }

      logger.debug('Retrieved order details from Shopify', {
        orderId,
        orderName: response.data.order.name
      });

      return {
        success: true,
        order: response.data.order
      };

    } catch (error) {
      logger.error('Error retrieving order from Shopify', {
        error,
        orderId
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Find line item IDs for return items
   */
  public async findLineItemIds(orderId: string, returnItems: ReturnItem[]): Promise<Map<string, string>> {
    const lineItemMap = new Map<string, string>();

    try {
      const orderResult = await this.getOrderDetails(orderId);
      
      if (!orderResult.success || !orderResult.order) {
        logger.error('Could not retrieve order for line item mapping', { orderId });
        return lineItemMap;
      }

      const order = orderResult.order;
      const lineItems = order.lineItems.edges.map((edge: any) => edge.node);

      for (const returnItem of returnItems) {
        // Try to match by SKU first
        let matchedLineItem = lineItems.find((lineItem: any) => 
          lineItem.sku === returnItem.sku || 
          lineItem.variant?.sku === returnItem.sku
        );

        // If no SKU match, try to match by product name
        if (!matchedLineItem) {
          matchedLineItem = lineItems.find((lineItem: any) => 
            lineItem.name.toLowerCase().includes(returnItem.name.toLowerCase()) ||
            lineItem.product?.title.toLowerCase().includes(returnItem.name.toLowerCase())
          );
        }

        if (matchedLineItem) {
          // Find corresponding fulfillment line item
          for (const fulfillment of order.fulfillments) {
            const fulfillmentLineItem = fulfillment.fulfillmentLineItems.edges
              .map((edge: any) => edge.node)
              .find((fli: any) => fli.lineItem.id === matchedLineItem.id);

            if (fulfillmentLineItem) {
              lineItemMap.set(returnItem.sku, fulfillmentLineItem.id.replace('gid://shopify/FulfillmentLineItem/', ''));
              logger.debug('Mapped return item to fulfillment line item', {
                sku: returnItem.sku,
                lineItemId: matchedLineItem.id,
                fulfillmentLineItemId: fulfillmentLineItem.id
              });
              break;
            }
          }
        } else {
          logger.warn('Could not find line item for return item', {
            sku: returnItem.sku,
            name: returnItem.name,
            orderId
          });
        }
      }

    } catch (error) {
      logger.error('Error mapping line item IDs', {
        error,
        orderId,
        returnItems: returnItems.map(i => ({ sku: i.sku, name: i.name }))
      });
    }

    return lineItemMap;
  }

  /**
   * Get product details including vendor information
   */
  public async getProductDetails(productId: string): Promise<{
    success: boolean;
    vendor?: string;
    tags?: string[];
    metafields?: any[];
    error?: string;
  }> {
    try {
      const query = `
        query getProduct($id: ID!) {
          product(id: $id) {
            id
            vendor
            tags
            metafields(first: 10) {
              edges {
                node {
                  namespace
                  key
                  value
                }
              }
            }
          }
        }
      `;

      const variables = {
        id: `gid://shopify/Product/${productId}`
      };

      const response = await this.client.post('', {
        query,
        variables
      });

      if (response.data.errors) {
        logger.error('Shopify GraphQL errors in getProductDetails', {
          errors: response.data.errors,
          productId
        });
        return {
          success: false,
          error: response.data.errors[0]?.message || 'GraphQL error'
        };
      }

      const product = response.data.data?.product;
      if (!product) {
        return {
          success: false,
          error: 'Product not found'
        };
      }

      const metafields = product.metafields?.edges?.map((edge: any) => edge.node) || [];

      logger.info('Successfully fetched product details from Shopify', {
        productId,
        vendor: product.vendor,
        tagCount: product.tags?.length || 0,
        metafieldCount: metafields.length
      });

      return {
        success: true,
        vendor: product.vendor,
        tags: product.tags || [],
        metafields
      };
    } catch (error: any) {
      logger.error('Failed to get product details from Shopify', {
        error: error.response?.data || error.message,
        productId
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get variant details including product information
   */
  public async getVariantDetails(variantId: string): Promise<{
    success: boolean;
    productId?: string;
    vendor?: string;
    sku?: string;
    error?: string;
  }> {
    try {
      const query = `
        query getVariant($id: ID!) {
          productVariant(id: $id) {
            id
            sku
            product {
              id
              vendor
            }
          }
        }
      `;

      const variables = {
        id: `gid://shopify/ProductVariant/${variantId}`
      };

      const response = await this.client.post('', {
        query,
        variables
      });

      if (response.data.errors) {
        logger.error('Shopify GraphQL errors in getVariantDetails', {
          errors: response.data.errors,
          variantId
        });
        return {
          success: false,
          error: response.data.errors[0]?.message || 'GraphQL error'
        };
      }

      const variant = response.data.data?.productVariant;
      if (!variant) {
        return {
          success: false,
          error: 'Variant not found'
        };
      }

      const productId = variant.product?.id?.replace('gid://shopify/Product/', '');

      logger.info('Successfully fetched variant details from Shopify', {
        variantId,
        productId,
        vendor: variant.product?.vendor,
        sku: variant.sku
      });

      return {
        success: true,
        productId,
        vendor: variant.product?.vendor,
        sku: variant.sku
      };
    } catch (error: any) {
      logger.error('Failed to get variant details from Shopify', {
        error: error.response?.data || error.message,
        variantId
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Process exchange for approved return items
   */
  public async processExchange(
    orderId: string,
    returnItems: ReturnItem[],
    customerEmail: string,
    returnId: string
  ): Promise<{ success: boolean; shopifyReturnId?: string; error?: string }> {
    try {
      // Get line item mappings
      const lineItemMap = await this.findLineItemIds(orderId, returnItems);

      if (lineItemMap.size === 0) {
        logger.error('No line items could be mapped for exchange', {
          orderId,
          returnId,
          returnItems: returnItems.map(i => ({ sku: i.sku, name: i.name }))
        });
        return {
          success: false,
          error: 'Could not map return items to order line items'
        };
      }

      // Create return request
      const returnRequest: ShopifyReturnRequest = {
        orderId,
        lineItems: returnItems
          .filter(item => lineItemMap.has(item.sku))
          .map(item => ({
            lineItemId: lineItemMap.get(item.sku)!,
            quantity: item.qty,
            reason: item.reason
          })),
        notifyCustomer: true,
        note: `Return processed via automation system. Return ID: ${returnId}`
      };

      // Create the return in Shopify
      const result = await this.createReturn(returnRequest);

      if (result.success) {
        logger.info('Exchange processed successfully in Shopify', {
          orderId,
          returnId,
          shopifyReturnId: result.returnId,
          processedItems: returnRequest.lineItems.length
        });
      }

      return result;

    } catch (error) {
      logger.error('Error processing exchange in Shopify', {
        error,
        orderId,
        returnId
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test Shopify API connection
   */
  public async testConnection(): Promise<boolean> {
    try {
      const query = `
        query {
          shop {
            name
            email
            domain
          }
        }
      `;

      const response = await this.client.request(query);
      
      if (response.data?.shop) {
        logger.info('Shopify API connection test successful', {
          shopName: response.data.shop.name,
          domain: response.data.shop.domain
        });
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Shopify API connection test failed', { error });
      return false;
    }
  }
}
