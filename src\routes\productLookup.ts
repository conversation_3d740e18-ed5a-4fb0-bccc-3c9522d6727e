import { Router, Request, Response } from 'express';
import { ShopifyService } from '../services/ShopifyService';
import { SupplierMapper } from '../services/SupplierMapper';
import logger from '../utils/logger';

const router = Router();

interface ProductLookupRequest {
  product_id?: string;
  variant_id?: string;
  sku?: string;
  title?: string;
  price?: number;
  quantity?: number;
  customer_email?: string;
  customer_name?: string;
  order_id?: string;
  return_id?: string;
  return_reason?: string;
}

interface ProductLookupResponse {
  success: boolean;
  input_data: ProductLookupRequest;
  shopify_data?: {
    product?: any;
    variant?: any;
    vendor?: string;
    vendor_details?: any;
    tags?: string[];
    metafields?: any[];
    full_product_info?: any;
  };
  supplier_mapping?: {
    supplier_name: string;
    supplier_email?: string;
    requires_manual_review?: boolean;
    mapping_method: 'vendor_name' | 'sku_prefix' | 'unknown';
    supplier_config?: any;
  };
  return_item_details?: any;
  error?: string;
  timestamp: string;
}

/**
 * POST /api/product-lookup
 * Get full vendor details from Shopify based on webhook data
 */
router.post('/api/product-lookup', async (req: Request, res: Response) => {
  try {
    const requestData: ProductLookupRequest = req.body;

    logger.info('Product lookup request received', {
      product_id: requestData.product_id,
      variant_id: requestData.variant_id,
      sku: requestData.sku,
      return_id: requestData.return_id
    });

    const response: ProductLookupResponse = {
      success: false,
      input_data: requestData,
      timestamp: new Date().toISOString()
    };

    // Validate input
    if (!requestData.product_id && !requestData.variant_id) {
      response.error = 'Either product_id or variant_id is required';
      res.status(400).json(response);
      return;
    }

    const shopifyService = new ShopifyService();
    const supplierMapper = new SupplierMapper();

    // Step 1: Get product details from Shopify
    let vendor: string | undefined;

    if (requestData.product_id) {
      logger.info('Fetching product details from Shopify', {
        product_id: requestData.product_id
      });

      const productResult = await shopifyService.getProductDetails(requestData.product_id);
      
      if (productResult.success) {
        vendor = productResult.vendor;
        response.shopify_data = {
          vendor: productResult.vendor,
          vendor_details: productResult.vendor_details,
          tags: productResult.tags,
          metafields: productResult.metafields,
          full_product_info: productResult.product
        };

        logger.info('Successfully retrieved product details', {
          product_id: requestData.product_id,
          vendor: productResult.vendor,
          tags_count: productResult.tags?.length || 0
        });
      } else {
        logger.warn('Failed to get product details', {
          product_id: requestData.product_id,
          error: productResult.error
        });
      }
    }

    // Step 2: Try variant lookup if product lookup failed or no vendor found
    if ((!vendor || !response.shopify_data) && requestData.variant_id) {
      logger.info('Fetching variant details from Shopify', {
        variant_id: requestData.variant_id
      });

      const variantResult = await shopifyService.getVariantDetails(requestData.variant_id);
      
      if (variantResult.success) {
        vendor = variantResult.vendor;
        
        if (!response.shopify_data) {
          response.shopify_data = {};
        }
        response.shopify_data.vendor = variantResult.vendor;
        response.shopify_data.variant = {
          id: requestData.variant_id,
          sku: variantResult.sku,
          product_id: variantResult.productId
        };

        logger.info('Successfully retrieved variant details', {
          variant_id: requestData.variant_id,
          vendor: variantResult.vendor,
          sku: variantResult.sku
        });
      } else {
        logger.warn('Failed to get variant details', {
          variant_id: requestData.variant_id,
          error: variantResult.error
        });
      }
    }

    // Step 3: Map to supplier
    const returnItem = {
      sku: requestData.sku || 'unknown',
      name: requestData.title || 'Unknown Product',
      qty: 1,
      reason: requestData.return_reason || 'No reason provided',
      vendor_name: vendor,
      product_id: requestData.product_id,
      variant_id: requestData.variant_id
    };

    const supplierName = supplierMapper.mapItemToSupplier(returnItem);
    const supplierConfig = supplierMapper.getSupplierConfig(supplierName);

    let mappingMethod: 'vendor_name' | 'sku_prefix' | 'unknown' = 'unknown';
    if (vendor && supplierName !== 'Unknown') {
      mappingMethod = 'vendor_name';
    } else if (supplierName !== 'Unknown') {
      mappingMethod = 'sku_prefix';
    }

    response.supplier_mapping = {
      supplier_name: supplierName,
      supplier_email: supplierConfig?.email,
      requires_manual_review: supplierConfig?.requiresManualReview || false,
      mapping_method: mappingMethod,
      supplier_config: supplierConfig ? {
        name: supplierConfig.name,
        email: supplierConfig.email,
        vendor_names: supplierConfig.vendorNames,
        sku_prefixes: supplierConfig.skuPrefixes,
        requires_manual_review: supplierConfig.requiresManualReview
      } : null
    };

    // Include the complete return item details
    response.return_item_details = returnItem;

    logger.info('Supplier mapping completed', {
      supplier_name: supplierName,
      mapping_method: mappingMethod,
      vendor: vendor,
      sku: requestData.sku
    });

    // Step 4: Determine success
    response.success = !!(response.shopify_data?.vendor || supplierName !== 'Unknown');

    if (!response.success) {
      response.error = 'Could not retrieve vendor information or map to supplier';
    }

    res.json(response);

  } catch (error: any) {
    logger.error('Product lookup failed', {
      error: error.message,
      stack: error.stack,
      request_body: req.body
    });

    res.status(500).json({
      success: false,
      input_data: req.body,
      error: 'Internal server error during product lookup',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/product-lookup/test
 * Test endpoint with sample webhook data
 */
router.get('/api/product-lookup/test', async (req: Request, res: Response) => {
  const sampleData: ProductLookupRequest = {
    product_id: "9698464760088",
    variant_id: "49924238278936",
    sku: "036265ec-6c5a-4273-9e75-d15e7f50823b",
    title: "Vessel - Air [Emerald]",
    price: 20,
    quantity: 1,
    customer_email: "<EMAIL>",
    customer_name: "Nathan Kim",
    order_id: "6437418565912",
    return_id: "687534ee0e16543e5424053b",
    return_reason: "Others"
  };

  logger.info('Test product lookup request');

  try {
    const requestData = sampleData;

    const response: ProductLookupResponse = {
      success: false,
      input_data: requestData,
      timestamp: new Date().toISOString()
    };

    const shopifyService = new ShopifyService();
    const supplierMapper = new SupplierMapper();

    // Get product details from Shopify
    let vendor: string | undefined;

    if (requestData.product_id) {
      const productResult = await shopifyService.getProductDetails(requestData.product_id);

      if (productResult.success) {
        vendor = productResult.vendor;
        response.shopify_data = {
          vendor: productResult.vendor,
          tags: productResult.tags,
          metafields: productResult.metafields
        };
      }
    }

    // Map to supplier - make everything dynamic
    const returnItem = {
      sku: requestData.sku || 'unknown',
      name: requestData.title || 'Unknown Product',
      qty: requestData.quantity || 1, // Dynamic quantity
      reason: requestData.return_reason || 'No reason provided',
      vendor_name: vendor,
      product_id: requestData.product_id,
      variant_id: requestData.variant_id,
      price: requestData.price,
      customer_email: requestData.customer_email,
      customer_name: requestData.customer_name,
      order_id: requestData.order_id,
      return_id: requestData.return_id
    };

    const supplierName = supplierMapper.mapItemToSupplier(returnItem);
    const supplierConfig = supplierMapper.getSupplierConfig(supplierName);

    let mappingMethod: 'vendor_name' | 'sku_prefix' | 'unknown' = 'unknown';
    if (vendor && supplierName !== 'Unknown') {
      mappingMethod = 'vendor_name';
    } else if (supplierName !== 'Unknown') {
      mappingMethod = 'sku_prefix';
    }

    response.supplier_mapping = {
      supplier_name: supplierName,
      supplier_email: supplierConfig?.email,
      requires_manual_review: supplierConfig?.requiresManualReview || false,
      mapping_method: mappingMethod
    };

    response.success = !!(response.shopify_data?.vendor || supplierName !== 'Unknown');

    if (!response.success) {
      response.error = 'Could not retrieve vendor information or map to supplier';
    }

    res.json(response);

  } catch (error: any) {
    logger.error('Test product lookup failed', { error: error.message });
    res.status(500).json({
      success: false,
      input_data: sampleData,
      error: 'Internal server error during test lookup',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/suppliers
 * Get all configured suppliers
 */
router.get('/api/suppliers', (req: Request, res: Response) => {
  try {
    const supplierMapper = new SupplierMapper();
    const suppliers = supplierMapper.getAllSuppliers();

    res.json({
      success: true,
      suppliers: suppliers.map(supplier => ({
        name: supplier.name,
        email: supplier.email,
        vendor_names: supplier.vendorNames,
        sku_prefixes: supplier.skuPrefixes,
        requires_manual_review: supplier.requiresManualReview
      })),
      count: suppliers.length,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    logger.error('Failed to get suppliers', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve suppliers',
      timestamp: new Date().toISOString()
    });
  }
});

export default router;
