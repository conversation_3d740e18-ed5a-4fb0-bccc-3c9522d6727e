# Shopify API Setup Guide

## Overview

To connect to the Shopify API and fetch vendor information for return requests, you need to set up proper authentication and configure the required environment variables.

## 🔧 What You Need

1. **Shopify Store Access** - Admin access to your Shopify store
2. **API Access Token** - Either through a Custom App or Private App
3. **Required Permissions** - Specific scopes to read product data

## 📋 Step-by-Step Setup

### Option 1: Custom App (Recommended for Production)

1. **Go to Shopify Admin**
   - Navigate to: `Settings` → `Apps and sales channels` → `Develop apps`

2. **Create a Custom App**
   - Click "Create an app"
   - Enter app name: "Return Prime Integration"
   - Click "Create app"

3. **Configure API Scopes**
   - Click "Configure Admin API scopes"
   - Enable the following scopes:
     - `read_products` - To fetch product details and vendor information
     - `read_orders` - To access order information (if needed)
     - `read_inventory` - To check product availability (optional)

4. **Install the App**
   - Click "Install app"
   - Confirm the installation

5. **Get Access Token**
   - After installation, you'll see the "Admin API access token"
   - Copy this token - you'll need it for the environment variables

### Option 2: Private App (Legacy - for existing setups)

1. **Enable Private App Development**
   - Go to: `Settings` → `Apps and sales channels` → `Develop apps`
   - Click "Allow custom app development" (if not already enabled)

2. **Create Private App**
   - Click "Create a private app"
   - Fill in app details
   - Configure the same scopes as above

## 🔑 Environment Variables

Create a `.env` file in your project root with the following variables:

```env
# Shopify Configuration
SHOPIFY_STORE_URL=your-store-name.myshopify.com
SHOPIFY_ACCESS_TOKEN=shpat_your_access_token_here
SHOPIFY_API_VERSION=2025-07

# Return Prime Configuration  
RETURN_PRIME_API_URL=https://api.returnprime.com/v1
RETURN_PRIME_ADMIN_ACCESS_TOKEN=de5fe93b536d04a451edd984d305577e02f3c424a40f8e8c2293a6bc4de229b4

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Supplier Email Addresses
SMOKEDROP_EMAIL=<EMAIL>
BUDDIFY_EMAIL=<EMAIL>
CANNA_RIVER_EMAIL=<EMAIL>
DISCREET_SMOKER_EMAIL=<EMAIL>
INTERNAL_REVIEW_EMAIL=<EMAIL>
```

## 🔍 How to Find Your Store Information

### Store URL Format
- Your store URL should be: `your-store-name.myshopify.com`
- Example: If your store is `https://mybakeshop.myshopify.com/admin`, use `mybakeshop.myshopify.com`

### Access Token Format
- Custom app tokens start with: `shpat_`
- Example: `shpat_1234567890abcdef1234567890abcdef`

## ✅ Testing the Connection

1. **Run the Test Script**
   ```bash
   node test-vendor-enrichment.js
   ```

2. **Expected Output**
   - Should successfully connect to Shopify
   - Fetch product details including vendor information
   - Map products to configured suppliers

3. **Common Issues and Solutions**

   **Issue**: `"this.client.post is not a function"`
   - **Solution**: ✅ Fixed - Updated to use `client.request()` method

   **Issue**: `"Unauthorized"` or `401` error
   - **Solution**: Check your access token and store URL
   - Verify the app is installed and has correct permissions

   **Issue**: `"API version not supported"`
   - **Solution**: ✅ Fixed - Updated to use API version `2025-07`

   **Issue**: `"Field doesn't exist"` errors
   - **Solution**: Ensure your app has `read_products` scope enabled

## 🏪 Required Shopify Permissions

Your Shopify app needs these specific permissions:

| Permission | Scope | Purpose |
|------------|-------|---------|
| **Products** | `read_products` | Fetch product details and vendor information |
| **Orders** | `read_orders` | Access order data (optional, for order validation) |
| **Inventory** | `read_inventory` | Check product availability (optional) |

## 🔄 API Usage Flow

1. **Return Prime Webhook** → Provides `product_id` and `variant_id`
2. **Shopify GraphQL Query** → Fetches vendor information using product ID
3. **Vendor Mapping** → Maps Shopify vendor to configured supplier
4. **Email Notification** → Sends return details to correct supplier

## 📊 GraphQL Queries Used

The system uses these GraphQL queries to fetch vendor information:

### Product Query
```graphql
query getProduct($id: ID!) {
  product(id: $id) {
    id
    vendor
    tags
    metafields(first: 10) {
      edges {
        node {
          namespace
          key
          value
        }
      }
    }
  }
}
```

### Variant Query (Fallback)
```graphql
query getVariant($id: ID!) {
  productVariant(id: $id) {
    id
    sku
    product {
      id
      vendor
    }
  }
}
```

## 🚨 Security Best Practices

1. **Keep Access Tokens Secret**
   - Never commit tokens to version control
   - Use environment variables
   - Rotate tokens regularly

2. **Minimal Permissions**
   - Only request scopes you actually need
   - Review permissions periodically

3. **Rate Limiting**
   - Shopify has rate limits (1000 points per minute)
   - The system includes retry logic for rate limit handling

## 🔧 Troubleshooting

### Check API Connection
```bash
# Test basic connectivity
curl -X POST \
  https://your-store.myshopify.com/admin/api/2025-07/graphql.json \
  -H 'Content-Type: application/json' \
  -H 'X-Shopify-Access-Token: your_token' \
  -d '{"query": "{ shop { name } }"}'
```

### Verify Product Data
- Ensure products in your Shopify store have vendor information set
- Check that vendor names match your supplier configuration
- Update product data if vendor field is empty

## 📞 Support

If you encounter issues:
1. Check the application logs for detailed error messages
2. Verify all environment variables are set correctly
3. Test the Shopify API connection independently
4. Review Shopify's API documentation for any changes
