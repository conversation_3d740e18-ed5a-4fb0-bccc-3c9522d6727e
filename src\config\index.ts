import dotenv from 'dotenv';
import { AppConfig, SupplierConfig } from '../types';

dotenv.config();

const requiredEnvVars = [
  'RETURN_PRIME_API_URL',
  'RETURN_PRIME_ADMIN_ACCESS_TOKEN',
  'RETURN_PRIME_WEBHOOK_SECRET',
  'SHOPIFY_STORE_URL',
  'SHOPIFY_ACCESS_TOKEN',
  'EMAIL_HOST',
  'EMAIL_USER',
  'EMAIL_PASS',
  'EMAIL_FROM'
];

// Validate required environment variables
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

// Supplier configurations
const suppliers: SupplierConfig[] = [
  {
    name: 'SmokeDrop',
    email: process.env.SMOKEDROP_EMAIL || '<EMAIL>',
    requiresManualReview: false,
    skuPrefixes: ['SMOKEDROP-', 'SD-'],
    vendorNames: ['SmokeDrop', 'Smoke Drop']
  },
  {
    name: 'Buddify',
    email: process.env.BUDDIFY_EMAIL || '<EMAIL>',
    requiresManualReview: false,
    skuPrefixes: ['BUDDIFY-', 'BUD-'],
    vendorNames: ['Buddify']
  },
  {
    name: 'Canna River',
    email: process.env.CANNA_RIVER_EMAIL || '<EMAIL>',
    requiresManualReview: true, // Requires manual review as specified
    skuPrefixes: ['CANNA-', 'CR-', 'CANNARIVER-'],
    vendorNames: ['Canna River', 'CannaRiver']
  },
  {
    name: 'Discreet Smoker',
    email: process.env.DISCREET_SMOKER_EMAIL || '<EMAIL>',
    requiresManualReview: false,
    skuPrefixes: ['DISCREET-', 'DS-'],
    vendorNames: ['Discreet Smoker', 'DiscreetSmoker']
  }
];

export const config: AppConfig = {
  port: parseInt(process.env.PORT || '3000', 10),
  nodeEnv: process.env.NODE_ENV || 'development',
  webhookSecret: process.env.RETURN_PRIME_WEBHOOK_SECRET!,

  returnPrime: {
    apiUrl: process.env.RETURN_PRIME_API_URL || 'https://api.returnprime.com/v1',
    adminAccessToken: process.env.RETURN_PRIME_ADMIN_ACCESS_TOKEN || 'de5fe93b536d04a451edd984d305577e02f3c424a40f8e8c2293a6bc4de229b4'
  },
  
  shopify: {
    storeUrl: process.env.SHOPIFY_STORE_URL!,
    accessToken: process.env.SHOPIFY_ACCESS_TOKEN!,
    apiVersion: process.env.SHOPIFY_API_VERSION || '2025-07'
  },
  
  email: {
    host: process.env.EMAIL_HOST!,
    port: parseInt(process.env.EMAIL_PORT || '587', 10),
    secure: process.env.EMAIL_SECURE === 'true',
    user: process.env.EMAIL_USER!,
    pass: process.env.EMAIL_PASS!,
    from: process.env.EMAIL_FROM!
  },
  
  googleSheets: process.env.GOOGLE_SHEETS_ID ? {
    sheetsId: process.env.GOOGLE_SHEETS_ID,
    serviceAccountEmail: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!,
    privateKey: process.env.GOOGLE_PRIVATE_KEY!.replace(/\\n/g, '\n')
  } : undefined,
  
  airtable: process.env.AIRTABLE_API_KEY ? {
    apiKey: process.env.AIRTABLE_API_KEY,
    baseId: process.env.AIRTABLE_BASE_ID!,
    tableName: process.env.AIRTABLE_TABLE_NAME || 'Returns'
  } : undefined,
  

  
  suppliers,
  maxRetryAttempts: parseInt(process.env.MAX_RETRY_ATTEMPTS || '3', 10),
  retryDelayMs: parseInt(process.env.RETRY_DELAY_MS || '5000', 10),
  internalReviewEmail: process.env.INTERNAL_REVIEW_EMAIL || '<EMAIL>'
};

export default config;
