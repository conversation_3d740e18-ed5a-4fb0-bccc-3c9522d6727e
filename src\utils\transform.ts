import { ReturnPrimeWebhookPayload, ReturnItem } from '../types';
import logger from './logger';

/**
 * Transform Return Prime webhook payload to our internal format
 */
export function transformReturnPrimePayload(payload: ReturnPrimeWebhookPayload): {
  event_type: string;
  return_id: string;
  order_id: string;
  customer_email: string;
  customer_name?: string;
  items: ReturnItem[];
  created_at: string;
  status: string;
  exchange?: boolean;
  refund_amount?: number;
  notes?: string;
} {
  const request = payload.request;
  
  // Determine event type based on status and approval state
  let event_type = 'request.created';

  // Check the main status first
  if (request.status) {
    switch (request.status.toLowerCase()) {
      case 'approved':
        event_type = 'request.approved';
        break;
      case 'rejected':
        event_type = 'request.rejected';
        break;
      case 'inspected':
        event_type = 'request.inspected';
        break;
      case 'received':
        event_type = 'request.received';
        break;
      case 'archived':
        event_type = 'request.archived';
        break;
      case 'processing':
        event_type = 'request.processing';
        break;
      case 'completed':
        event_type = 'request.completed';
        break;
      default:
        event_type = `request.${request.status.toLowerCase()}`;
    }
  }

  // Also check individual status objects if they exist
  if (request.approved?.status === true) {
    event_type = 'request.approved';
  } else if (request.rejected?.status === true) {
    event_type = 'request.rejected';
  } else if (request.received?.status === true) {
    event_type = 'request.received';
  } else if (request.inspected?.status === true) {
    event_type = 'request.inspected';
  } else if (request.archived?.status === true) {
    event_type = 'request.archived';
  }

  // Check if any line items have been refunded
  const hasRefundedItems = request.line_items?.some(item =>
    (item.refund as any)?.status === 'refunded' || item.refund?.refunded_at
  );
  if (hasRefundedItems) {
    event_type = 'request.refunded';
  }

  // Transform line items to our format (with safe access)
  const items: ReturnItem[] = (request.line_items || []).map(lineItem => ({
    sku: lineItem.original_product?.sku || 'unknown-sku',
    name: lineItem.original_product?.title || 'Unknown Product',
    qty: lineItem.quantity || 1,
    reason: lineItem.reason || 'No reason provided',
    images: lineItem.original_product?.image?.src ? [lineItem.original_product.image.src] : undefined,
    vendor_name: undefined, // Return Prime doesn't provide vendor in this format
    product_id: lineItem.original_product?.product_id?.toString() || undefined,
    variant_id: lineItem.original_product?.variant_id?.toString() || undefined,
    line_item_id: lineItem.id?.toString() || undefined
  }));

  // Check if this is an exchange (with safe access)
  const isExchange = request.request_type === 'exchange' ||
                    (request.line_items || []).some(item => item.refund?.requested_mode === 'exchange');

  // Calculate total refund amount (with safe access)
  const refund_amount = (request.line_items || []).reduce((total, item) => {
    return total + (item.presentment_price?.actual_amount || 0);
  }, 0);

  const transformed = {
    event_type,
    return_id: request.id || 'unknown',
    order_id: request.order?.id?.toString() || 'unknown',
    customer_email: request.customer?.email || '<EMAIL>',
    customer_name: request.customer?.name || undefined,
    items,
    created_at: request.created_at || new Date().toISOString(),
    status: request.status || 'unknown',
    exchange: isExchange,
    refund_amount,
    notes: (request.line_items || []).map(item => item.notes).filter(Boolean).join('; ') || undefined
  };

  logger.info('Transformed Return Prime payload', {
    originalId: request.id,
    transformedEventType: event_type,
    itemCount: items.length,
    isExchange,
    refundAmount: refund_amount
  });

  return transformed;
}

/**
 * Get event type from Return Prime headers
 */
export function getEventTypeFromHeaders(headers: any): string {
  const topic = headers['x-rp-topic'];
  if (topic) {
    // Convert Return Prime topic format to our event format
    // e.g., "request/approved" -> "request.approved"
    return topic.replace('/', '.');
  }
  return 'request.unknown';
}

/**
 * Extract store information from Return Prime headers
 */
export function getStoreFromHeaders(headers: any): string | undefined {
  return headers['x-rp-store'];
}
