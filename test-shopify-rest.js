const axios = require('axios');
require('dotenv').config();

async function testShopifyREST() {
  console.log('🔍 Testing Shopify REST API Connection...\n');

  const storeUrl = process.env.SHOPIFY_STORE_URL;
  const accessToken = process.env.SHOPIFY_ACCESS_TOKEN;
  const apiVersion = process.env.SHOPIFY_API_VERSION || '2025-07';

  if (!storeUrl || !accessToken) {
    console.log('❌ Missing Shopify credentials');
    return;
  }

  const baseURL = `https://${storeUrl}/admin/api/${apiVersion}`;
  
  console.log('📋 Testing with:');
  console.log(`   Store URL: ${storeUrl}`);
  console.log(`   Base URL: ${baseURL}`);
  console.log(`   Access Token: ${accessToken.substring(0, 10)}...`);
  console.log('');

  try {
    // Test 1: Shop info
    console.log('🏪 Test 1: Shop information...');
    const shopResponse = await axios.get(`${baseURL}/shop.json`, {
      headers: {
        'X-Shopify-Access-Token': accessToken,
        'Content-Type': 'application/json'
      }
    });

    if (shopResponse.data?.shop) {
      console.log('✅ Shop REST API successful!');
      console.log(`   Shop Name: ${shopResponse.data.shop.name}`);
      console.log(`   Domain: ${shopResponse.data.shop.domain}`);
      console.log(`   Email: ${shopResponse.data.shop.email}`);
      console.log(`   Plan: ${shopResponse.data.shop.plan_name}`);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Products
    console.log('📦 Test 2: Products...');
    const productsResponse = await axios.get(`${baseURL}/products.json?limit=3`, {
      headers: {
        'X-Shopify-Access-Token': accessToken,
        'Content-Type': 'application/json'
      }
    });

    if (productsResponse.data?.products) {
      const products = productsResponse.data.products;
      console.log(`✅ Found ${products.length} products:`);
      products.forEach((product, index) => {
        console.log(`   ${index + 1}. ${product.title}`);
        console.log(`      ID: ${product.id}`);
        console.log(`      Vendor: ${product.vendor || 'Not specified'}`);
        console.log(`      Handle: ${product.handle}`);
        console.log('');
      });
    }

  } catch (error) {
    console.log('❌ REST API failed:', error.message);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Status Text: ${error.response.statusText}`);
      console.log(`   Data:`, error.response.data);
    }
  }
}

testShopifyREST().then(() => {
  console.log('🏁 REST API test completed!');
}).catch(error => {
  console.error('💥 Test failed:', error.message);
});
