const { createAdminApiClient } = require('@shopify/admin-api-client');
require('dotenv').config();

async function testShopifyConnection() {
  console.log('🔍 Testing Shopify API Connection...\n');

  // Check environment variables
  console.log('📋 Environment Variables:');
  console.log(`   SHOPIFY_STORE_URL: ${process.env.SHOPIFY_STORE_URL || 'NOT SET'}`);
  console.log(`   SHOPIFY_ACCESS_TOKEN: ${process.env.SHOPIFY_ACCESS_TOKEN ? 'SET (length: ' + process.env.SHOPIFY_ACCESS_TOKEN.length + ')' : 'NOT SET'}`);
  console.log(`   SHOPIFY_API_VERSION: ${process.env.SHOPIFY_API_VERSION || 'NOT SET'}`);
  console.log('');

  if (!process.env.SHOPIFY_STORE_URL || !process.env.SHOPIFY_ACCESS_TOKEN) {
    console.log('❌ Missing required Shopify environment variables');
    return;
  }

  try {
    // Create Shopify client
    console.log('🔗 Creating Shopify API client...');
    const client = createAdminApiClient({
      storeDomain: process.env.SHOPIFY_STORE_URL,
      accessToken: process.env.SHOPIFY_ACCESS_TOKEN,
      apiVersion: process.env.SHOPIFY_API_VERSION || '2025-07'
    });

    console.log('✅ Client created successfully');
    console.log('');

    // Test 1: Simple shop query
    console.log('🏪 Test 1: Basic shop information...');
    const shopQuery = `
      query {
        shop {
          name
          domain
          email
          plan {
            displayName
          }
        }
      }
    `;

    try {
      const response = await client.request(shopQuery);
      console.log('📡 Raw response:', JSON.stringify(response, null, 2));
      
      if (response.data?.shop) {
        console.log('✅ Shop query successful!');
        console.log(`   Shop Name: ${response.data.shop.name}`);
        console.log(`   Domain: ${response.data.shop.domain}`);
        console.log(`   Email: ${response.data.shop.email}`);
        console.log(`   Plan: ${response.data.shop.plan?.displayName || 'Unknown'}`);
      } else {
        console.log('❌ Shop query returned no data');
        console.log('   This might indicate an authentication issue');
      }
    } catch (error) {
      console.log('❌ Shop query failed:', error.message);
      if (error.response) {
        console.log('   Response status:', error.response.status);
        console.log('   Response data:', JSON.stringify(error.response.data, null, 2));
      }
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Products query
    console.log('📦 Test 2: Products query...');
    const productsQuery = `
      query {
        products(first: 3) {
          edges {
            node {
              id
              title
              vendor
              handle
            }
          }
        }
      }
    `;

    try {
      const response = await client.request(productsQuery);
      console.log('📡 Products response:', JSON.stringify(response, null, 2));
      
      if (response.data?.products?.edges) {
        const products = response.data.products.edges;
        console.log(`✅ Found ${products.length} products:`);
        products.forEach((productEdge, index) => {
          const product = productEdge.node;
          console.log(`   ${index + 1}. ${product.title}`);
          console.log(`      Vendor: ${product.vendor || 'Not specified'}`);
          console.log(`      Handle: ${product.handle}`);
        });
      } else {
        console.log('❌ Products query returned no data');
      }
    } catch (error) {
      console.log('❌ Products query failed:', error.message);
      if (error.response) {
        console.log('   Response status:', error.response.status);
        console.log('   Response data:', JSON.stringify(error.response.data, null, 2));
      }
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: API permissions check
    console.log('🔐 Test 3: Checking API permissions...');
    const permissionsQuery = `
      query {
        app {
          handle
          installation {
            accessScopes {
              handle
            }
          }
        }
      }
    `;

    try {
      const response = await client.request(permissionsQuery);
      console.log('📡 Permissions response:', JSON.stringify(response, null, 2));
      
      if (response.data?.app?.installation?.accessScopes) {
        const scopes = response.data.app.installation.accessScopes;
        console.log(`✅ App has ${scopes.length} permissions:`);
        scopes.forEach((scope, index) => {
          console.log(`   ${index + 1}. ${scope.handle}`);
        });
      } else {
        console.log('❌ Could not retrieve app permissions');
      }
    } catch (error) {
      console.log('❌ Permissions query failed:', error.message);
      console.log('   This might be normal for private apps');
    }

  } catch (error) {
    console.log('❌ Failed to create Shopify client:', error.message);
    console.log('   Check your store URL and access token');
  }
}

// Run the test
testShopifyConnection().then(() => {
  console.log('\n🏁 Shopify connection test completed!');
}).catch(error => {
  console.error('💥 Test failed:', error.message);
});
