import { ReturnItem, SupplierName, SupplierConfig, SupplierNotification } from '../types';
import { config } from '../config';
import logger from '../utils/logger';

export class SupplierMapper {
  private suppliers: SupplierConfig[];

  constructor() {
    this.suppliers = config.suppliers;
  }

  /**
   * Map a return item to its corresponding supplier
   */
  public mapItemToSupplier(item: ReturnItem): SupplierName {
    // First, try to match by vendor name if available (from Shopify)
    if (item.vendor_name) {
      for (const supplier of this.suppliers) {
        if (supplier.vendorNames.some(name =>
          name.toLowerCase() === item.vendor_name!.toLowerCase()
        )) {
          logger.info('Mapped item to supplier by vendor name from Shopify', {
            sku: item.sku,
            vendorName: item.vendor_name,
            supplier: supplier.name,
            productId: item.product_id,
            variantId: item.variant_id
          });
          return supplier.name;
        }
      }

      // If vendor name doesn't match any configured supplier, log it for review
      logger.warn('Vendor name from Shopify does not match any configured supplier', {
        sku: item.sku,
        vendorName: item.vendor_name,
        configuredSuppliers: this.suppliers.map(s => s.vendorNames).flat(),
        productId: item.product_id,
        variantId: item.variant_id
      });
    }

    // Fallback: try to match by SKU prefix
    for (const supplier of this.suppliers) {
      if (supplier.skuPrefixes.some(prefix =>
        item.sku.toUpperCase().startsWith(prefix.toUpperCase())
      )) {
        logger.info('Mapped item to supplier by SKU prefix (fallback)', {
          sku: item.sku,
          supplier: supplier.name,
          vendorName: item.vendor_name || 'not available'
        });
        return supplier.name;
      }
    }

    logger.warn('Could not map item to any supplier using vendor name or SKU prefix', {
      sku: item.sku,
      vendorName: item.vendor_name || 'not available',
      productId: item.product_id,
      variantId: item.variant_id,
      availableSuppliers: this.suppliers.map(s => s.name)
    });

    return 'Unknown';
  }

  /**
   * Get supplier configuration by name
   */
  public getSupplierConfig(supplierName: SupplierName): SupplierConfig | null {
    return this.suppliers.find(s => s.name === supplierName) || null;
  }

  /**
   * Group return items by supplier
   */
  public groupItemsBySupplier(items: ReturnItem[]): Map<SupplierName, ReturnItem[]> {
    const supplierGroups = new Map<SupplierName, ReturnItem[]>();

    for (const item of items) {
      const supplierName = this.mapItemToSupplier(item);
      
      if (!supplierGroups.has(supplierName)) {
        supplierGroups.set(supplierName, []);
      }
      
      supplierGroups.get(supplierName)!.push(item);
    }

    logger.info('Grouped items by supplier', {
      totalItems: items.length,
      suppliers: Array.from(supplierGroups.keys()),
      distribution: Object.fromEntries(
        Array.from(supplierGroups.entries()).map(([supplier, items]) => [
          supplier,
          items.length
        ])
      )
    });

    return supplierGroups;
  }

  /**
   * Create supplier notifications from grouped items
   */
  public createSupplierNotifications(
    supplierGroups: Map<SupplierName, ReturnItem[]>,
    returnId: string,
    orderId: string,
    customerEmail: string,
    customerName: string | undefined,
    createdAt: string
  ): SupplierNotification[] {
    const notifications: SupplierNotification[] = [];

    for (const [supplierName, items] of supplierGroups.entries()) {
      if (supplierName === 'Unknown') {
        logger.info('Creating internal notification for unknown supplier items', {
          returnId,
          items: items.map(i => ({ sku: i.sku, name: i.name }))
        });

        // Create a notification for internal review of unknown items
        notifications.push({
          supplier: 'Unknown',
          email: config.internalReviewEmail, // Send to internal team
          items,
          returnId,
          orderId,
          customerEmail,
          customerName,
          createdAt,
          requiresManualReview: true // Always requires manual review for unknown suppliers
        });
        continue;
      }

      const supplierConfig = this.getSupplierConfig(supplierName);
      if (!supplierConfig) {
        logger.error('Supplier configuration not found', { supplierName });
        continue;
      }

      notifications.push({
        supplier: supplierName,
        email: supplierConfig.email,
        items,
        returnId,
        orderId,
        customerEmail,
        customerName,
        createdAt,
        requiresManualReview: supplierConfig.requiresManualReview
      });
    }

    return notifications;
  }

  /**
   * Check if any items require manual review
   */
  public hasItemsRequiringManualReview(items: ReturnItem[]): boolean {
    return items.some(item => {
      const supplierName = this.mapItemToSupplier(item);
      const config = this.getSupplierConfig(supplierName);
      return config?.requiresManualReview || false;
    });
  }

  /**
   * Get all suppliers that require manual review
   */
  public getSuppliersRequiringManualReview(): SupplierConfig[] {
    return this.suppliers.filter(s => s.requiresManualReview);
  }

  /**
   * Validate supplier configuration
   */
  public validateConfiguration(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (this.suppliers.length === 0) {
      errors.push('No suppliers configured');
    }

    for (const supplier of this.suppliers) {
      if (!supplier.name || supplier.name.trim() === '') {
        errors.push(`Supplier missing name: ${JSON.stringify(supplier)}`);
      }

      if (!supplier.email || !supplier.email.includes('@')) {
        errors.push(`Invalid email for supplier ${supplier.name}: ${supplier.email}`);
      }

      if (!supplier.skuPrefixes || supplier.skuPrefixes.length === 0) {
        errors.push(`No SKU prefixes configured for supplier ${supplier.name}`);
      }

      if (!supplier.vendorNames || supplier.vendorNames.length === 0) {
        errors.push(`No vendor names configured for supplier ${supplier.name}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
