# Product Lookup API Documentation

## Overview

The Product Lookup API provides endpoints to fetch full vendor details from Shopify based on webhook data from Return Prime. This allows you to get complete product and supplier information for return processing.

## 🎯 Main Use Case

When you receive a Return Prime webhook with basic product information, you can use this API to:
1. **Get vendor information** from Shopify using product_id or variant_id
2. **Map the vendor** to your configured suppliers
3. **Get supplier contact details** for automated notifications

## 📋 Available Endpoints

### 1. POST /api/product-lookup

**Purpose**: Get full vendor details from Shopify based on webhook data

**Request Body**:
```json
{
  "product_id": "9698464760088",
  "variant_id": "49924238278936", 
  "sku": "036265ec-6c5a-4273-9e75-d15e7f50823b",
  "title": "Vessel - Air [Emerald]",
  "price": 20,
  "customer_email": "<EMAIL>",
  "customer_name": "<PERSON>",
  "order_id": "6437418565912",
  "return_id": "687534ee0e16543e5424053b",
  "return_reason": "Others"
}
```

**Response**:
```json
{
  "success": true,
  "input_data": {
    "product_id": "9698464760088",
    "variant_id": "49924238278936",
    "sku": "036265ec-6c5a-4273-9e75-d15e7f50823b",
    "title": "Vessel - Air [Emerald]",
    "price": 20,
    "customer_email": "<EMAIL>",
    "customer_name": "Nathan Kim",
    "order_id": "6437418565912",
    "return_id": "687534ee0e16543e5424053b",
    "return_reason": "Others"
  },
  "shopify_data": {
    "vendor": "SmokeDrop",
    "tags": ["cbd", "vape", "smokeshop"],
    "metafields": []
  },
  "supplier_mapping": {
    "supplier_name": "SmokeDrop",
    "supplier_email": "<EMAIL>",
    "requires_manual_review": false,
    "mapping_method": "vendor_name"
  },
  "timestamp": "2025-07-15T15:30:00.000Z"
}
```

### 2. GET /api/product-lookup/test

**Purpose**: Test the lookup functionality with sample webhook data

**Response**: Same as POST endpoint but uses predefined sample data

### 3. GET /api/suppliers

**Purpose**: Get all configured suppliers

**Response**:
```json
{
  "success": true,
  "suppliers": [
    {
      "name": "SmokeDrop",
      "email": "<EMAIL>",
      "vendor_names": ["SmokeDrop", "Smoke Drop"],
      "sku_prefixes": ["SMOKEDROP-", "SD-"],
      "requires_manual_review": false
    },
    {
      "name": "Canna River",
      "email": "<EMAIL>", 
      "vendor_names": ["Canna River", "CannaRiver"],
      "sku_prefixes": ["CANNA-", "CR-", "CANNARIVER-"],
      "requires_manual_review": false
    }
  ],
  "count": 2,
  "timestamp": "2025-07-15T15:30:00.000Z"
}
```

## 🔄 How It Works

### Step 1: Extract Product Information
The API takes webhook data containing:
- `product_id` - Shopify product ID
- `variant_id` - Shopify variant ID  
- `sku` - Product SKU
- Other return details

### Step 2: Query Shopify API
Using the product_id or variant_id, the system:
1. Calls Shopify GraphQL API to get product details
2. Extracts vendor information, tags, and metafields
3. Falls back to variant lookup if product lookup fails

### Step 3: Map to Supplier
The system maps the Shopify vendor to configured suppliers:
1. **Primary**: Match vendor name (e.g., "SmokeDrop" → SmokeDrop supplier)
2. **Fallback**: Match SKU prefix (e.g., "SMOKEDROP-123" → SmokeDrop supplier)
3. **Unknown**: Items that don't match any supplier

### Step 4: Return Complete Information
The response includes:
- Original webhook data
- Shopify vendor information
- Mapped supplier details
- Contact information for notifications

## 🧪 Testing

### Start the Server
```bash
npm run build
npm start
```

### Run the Test Script
```bash
node test-product-lookup-endpoint.js
```

### Manual Testing with curl

**Test with sample data**:
```bash
curl -X GET http://localhost:3000/api/product-lookup/test
```

**Test with custom data**:
```bash
curl -X POST http://localhost:3000/api/product-lookup \
  -H "Content-Type: application/json" \
  -d '{
    "product_id": "9698464760088",
    "variant_id": "49924238278936",
    "sku": "036265ec-6c5a-4273-9e75-d15e7f50823b",
    "title": "Vessel - Air [Emerald]",
    "customer_email": "<EMAIL>",
    "return_reason": "Defective"
  }'
```

**Get all suppliers**:
```bash
curl -X GET http://localhost:3000/api/suppliers
```

## 📊 Response Fields

### Success Response
- `success`: Boolean indicating if lookup was successful
- `input_data`: Original request data
- `shopify_data`: Vendor information from Shopify
  - `vendor`: Vendor name from Shopify
  - `tags`: Product tags
  - `metafields`: Custom fields
- `supplier_mapping`: Mapped supplier information
  - `supplier_name`: Name of mapped supplier
  - `supplier_email`: Contact email for notifications
  - `requires_manual_review`: Whether this supplier requires manual review
  - `mapping_method`: How the mapping was determined
- `timestamp`: When the lookup was performed

### Error Response
- `success`: false
- `input_data`: Original request data
- `error`: Error message describing what went wrong
- `timestamp`: When the error occurred

## 🔧 Integration Example

### With Return Prime Webhook
```javascript
// When you receive a Return Prime webhook
app.post('/webhook/return-prime', async (req, res) => {
  const webhookData = req.body;
  
  // Extract product information
  const productData = {
    product_id: webhookData.payload.request.line_items[0].original_product.product_id,
    variant_id: webhookData.payload.request.line_items[0].original_product.variant_id,
    sku: webhookData.payload.request.line_items[0].original_product.sku,
    title: webhookData.payload.request.line_items[0].original_product.title,
    customer_email: webhookData.payload.request.customer.email,
    customer_name: webhookData.payload.request.customer.name,
    return_id: webhookData.payload.request.id,
    return_reason: webhookData.payload.request.line_items[0].reason
  };
  
  // Get vendor details
  const lookupResponse = await axios.post('http://localhost:3000/api/product-lookup', productData);
  
  if (lookupResponse.data.success) {
    const supplier = lookupResponse.data.supplier_mapping;
    
    // Send email to supplier
    await sendEmailToSupplier(supplier.supplier_email, {
      returnId: productData.return_id,
      customerEmail: productData.customer_email,
      productTitle: productData.title,
      reason: productData.return_reason
    });
  }
});
```

## ⚠️ Error Handling

### Common Errors
1. **400 Bad Request**: Missing product_id or variant_id
2. **404 Not Found**: Product not found in Shopify
3. **401 Unauthorized**: Invalid Shopify credentials
4. **500 Internal Server Error**: Server or API issues

### Troubleshooting
1. **No vendor found**: Product may not have vendor set in Shopify
2. **Unknown supplier**: Vendor name doesn't match configured suppliers
3. **Shopify API errors**: Check access token and store URL

## 🔐 Security

- The endpoint doesn't require authentication (add if needed)
- Shopify credentials are stored securely in environment variables
- All requests and responses are logged for debugging

## 📈 Performance

- Shopify API calls are made on-demand
- Consider caching vendor information for frequently accessed products
- Rate limiting may be needed for high-volume usage
